/**
 * Socket service
 * Centralized service for socket.io operations
 */
import io from "socket.io-client";

// Socket instance cache
let socketInstance = null;

// Connection status
let connectionStatus = 'disconnected';

// Event listeners registry to avoid duplicate listeners
const eventListeners = new Map();

/**
 * Initialize socket connection
 * @param {string} userId - User ID for authentication
 * @returns {object} - Socket instance and connection methods
 */
export const initializeSocket = (userId) => {
  // Don't create a socket connection if there's no user ID
  if (!userId) {
    console.log("No user ID provided, not creating socket connection");
    return { socket: null, status: 'disconnected' };
  }

  // If socket already exists and is connected, return it
  if (socketInstance && socketInstance.connected) {
    console.log("Reusing existing socket connection:", socketInstance.id);
    return {
      socket: socketInstance,
      status: connectionStatus
    };
  }

  // Use relative URL to leverage Vite proxy in development
  const socketUrl = '/'; // Always use relative URL to leverage proxy

  console.log("Creating new socket connection to:", socketUrl, "(via proxy)");

  // Create new socket instance with optimized configuration
  socketInstance = io(socketUrl, {
    query: { userId },
    reconnection: true,
    reconnectionAttempts: 10, // Increased for serverless environments
    reconnectionDelay: 1000,
    reconnectionDelayMax: 10000, // Increased max delay
    timeout: 20000, // Increased timeout for serverless cold starts
    // Optimize transports for production
    transports: import.meta.env.PROD
      ? ['polling', 'websocket'] // Polling first for better Vercel compatibility
      : ['websocket', 'polling'],
    upgrade: true,
    forceNew: false,
    autoConnect: true,
    // Add additional options for better reliability
    rememberUpgrade: false, // Don't remember transport upgrades
  });

  // Add event listeners for debugging and error handling
  socketInstance.on('connect', () => {
    console.log("Socket connected in socketService:", socketInstance.id);
    connectionStatus = 'connected';
  });

  socketInstance.on('connect_error', (error) => {
    console.error("Socket connection error:", error);
    connectionStatus = 'error';
  });

  socketInstance.on('disconnect', (reason) => {
    console.log("Socket disconnected:", reason);
    connectionStatus = 'disconnected';

    // Auto-reconnect for certain disconnect reasons
    if (reason === 'io server disconnect') {
      // Server initiated disconnect, try to reconnect
      socketInstance.connect();
    }
  });

  socketInstance.on('reconnect', (attemptNumber) => {
    console.log("Socket reconnected after", attemptNumber, "attempts");
    connectionStatus = 'connected';
  });

  socketInstance.on('reconnect_error', (error) => {
    console.error("Socket reconnection error:", error);
    connectionStatus = 'error';
  });

  socketInstance.on('reconnect_failed', () => {
    console.error("Socket reconnection failed after all attempts");
    connectionStatus = 'failed';
  });

  // Update connection status
  connectionStatus = 'connecting';

  return {
    socket: socketInstance,
    status: connectionStatus
  };
};

/**
 * Register socket event listener with deduplication
 * @param {object} socket - Socket.io instance
 * @param {string} event - Event name
 * @param {function} callback - Event callback
 */
export const registerSocketEvent = (socket, event, callback) => {
  if (!socket) return;

  // Create unique key for this event + callback combination
  const callbackKey = `${event}_${callback.toString()}`;

  // Remove existing listener for this event + callback if exists
  if (eventListeners.has(callbackKey)) {
    socket.off(event, eventListeners.get(callbackKey));
    eventListeners.delete(callbackKey);
  }

  // Register new listener
  socket.on(event, callback);
  eventListeners.set(callbackKey, callback);
};

/**
 * Emit socket event with retry logic
 * @param {object} socket - Socket.io instance
 * @param {string} event - Event name
 * @param {any} data - Event data
 * @param {function} callback - Optional acknowledgement callback
 * @returns {Promise} - Resolves when event is sent or rejects on error
 */
export const emitSocketEvent = (socket, event, data, callback = null) => {
  return new Promise((resolve, reject) => {
    if (!socket || !socket.connected) {

      reject(new Error('Socket not connected'));
      return;
    }

    try {
      if (callback) {
        socket.emit(event, data, callback);
      } else {
        socket.emit(event, data);
      }
      resolve();
    } catch (error) {

      reject(error);
    }
  });
};

/**
 * Disconnect socket and clean up resources
 */
export const disconnectSocket = () => {
  if (socketInstance) {

    socketInstance.disconnect();
    socketInstance.removeAllListeners();
    socketInstance = null;
    connectionStatus = 'disconnected';
    eventListeners.clear();
  }
};

/**
 * Get current socket instance
 * @returns {object|null} - Current socket instance or null
 */
export const getSocketInstance = () => socketInstance;

/**
 * Get current connection status
 * @returns {string} - Connection status
 */
export const getConnectionStatus = () => connectionStatus;

/**
 * Update connection status
 * @param {string} status - New connection status
 */
export const updateConnectionStatus = (status) => {
  connectionStatus = status;
};
