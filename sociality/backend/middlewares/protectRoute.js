import User from "../models/userModel.js";
import jwt from "jsonwebtoken";

const protectRoute = async (req, res, next) => {
	try {
		// Check for session-specific cookie first, then fallback to default
		const sessionPath = req.query.session || '';
		const cookieName = sessionPath ? `jwt-sociality${sessionPath.replace(/\//g, '-')}` : 'jwt-sociality';

		let token = req.cookies[cookieName] || req.cookies.jwt || req.cookies['jwt-sociality'];

		// Debug logs removed for cleaner output

		if (!token) {
			console.error(`[protectRoute] No token found. Checked cookies: ${cookieName}, jwt, jwt-sociality`);
			console.error(`[protectRoute] Available cookies: ${Object.keys(req.cookies).join(', ')}`);
			return res.status(401).json({ message: "Unauthorized - No token provided" });
		}

		// Validate token format
		if (typeof token !== 'string' || token.split('.').length !== 3) {
			console.error(`[protectRoute] Invalid token format: ${typeof token}, parts: ${token.split('.').length}`);
			return res.status(401).json({ message: "Unauthorized - Invalid token format" });
		}

		// Validate JWT_SECRET exists
		if (!process.env.JWT_SECRET) {
			console.error("[protectRoute] JWT_SECRET environment variable not set");
			return res.status(500).json({ message: "Server configuration error" });
		}

		const decoded = jwt.verify(token, process.env.JWT_SECRET);

		// Check database connection before querying
		if (!User.db || User.db.readyState !== 1) {
			console.error("[protectRoute] Database not connected");
			return res.status(503).json({ message: "Database connection unavailable" });
		}

		const user = await User.findById(decoded.userId).select("-password");

		// Check if user exists after finding by ID
		if (!user) {
			console.error(`[protectRoute] User ${decoded.userId} not found in database`);
			// If user not found (e.g., deleted after token issuance), return unauthorized
			return res.status(401).json({ message: "Unauthorized - User not found" });
		}

		req.user = user;
		next();
	} catch (err) {
		console.error("[protectRoute] Authentication error:", err.message);

		// Only log errors, not successful authentications
		if (err.name === 'JsonWebTokenError') {
			return res.status(401).json({ message: "Unauthorized - Invalid token" });
		} else if (err.name === 'TokenExpiredError') {
			return res.status(401).json({ message: "Unauthorized - Token expired" });
		} else if (err.name === 'MongoNetworkError' || err.name === 'MongoTimeoutError') {
			return res.status(503).json({ message: "Database connection error" });
		}

		res.status(500).json({ message: "Authentication service error" });
	}
};

export default protectRoute;
