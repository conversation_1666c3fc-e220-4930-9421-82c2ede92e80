import mongoose from "mongoose";
import logger from "../utils/logger.js";

const connectDB = async () => {
	try {
		// Validate MONGO_URI exists
		if (!process.env.MONGO_URI) {
			throw new Error("MONGO_URI environment variable is not set");
		}

		// Configure mongoose for better Vercel compatibility
		mongoose.set('strictQuery', false);

		// useNewUrlParser and useUnifiedTopology are deprecated and default to true in Mongoose 6+
		const conn = await mongoose.connect(process.env.MONGO_URI, {
			// Connection options optimized for serverless
			serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
			socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
			maxPoolSize: 10, // Maintain up to 10 socket connections
			minPoolSize: 1, // Maintain at least 1 socket connection
			maxIdleTimeMS: 30000, // Close connections after 30s of inactivity
			bufferCommands: false, // Disable mongoose buffering
			bufferMaxEntries: 0, // Disable mongoose buffering
		});

		logger.info(`MongoDB Connected: ${conn.connection.host}`);

		// Handle connection events
		mongoose.connection.on('error', (err) => {
			logger.error('MongoDB connection error:', err);
		});

		mongoose.connection.on('disconnected', () => {
			logger.warn('MongoDB disconnected');
		});

		mongoose.connection.on('reconnected', () => {
			logger.info('MongoDB reconnected');
		});

	} catch (error) {
		logger.error(`Database connection error: ${error.message}`, error);

		// In serverless environments, don't exit the process
		if (process.env.NODE_ENV === 'production') {
			logger.error('MongoDB connection failed in production environment');
			// Don't exit in serverless - let the function handle the error
		} else {
			process.exit(1);
		}
	}
};

export default connectDB;
