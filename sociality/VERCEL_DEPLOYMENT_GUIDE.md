# Vercel Deployment Guide for Sociality

This guide will help you fix the Socket.IO connection issues and API errors on your Vercel deployment.

## Issues Fixed

1. **CORS Configuration**: Updated to include your actual Vercel domain
2. **Socket.IO Transport Order**: Optimized for serverless environment
3. **Error Handling**: Improved API and authentication error handling
4. **Session Configuration**: Enhanced for Vercel compatibility
5. **Environment Variables**: Better validation and fallbacks

## Environment Variables Setup

1. Go to your Vercel dashboard
2. Navigate to your project settings
3. Go to "Environment Variables" section
4. Add the following variables (use `.env.production.example` as reference):

### Required Variables:
```
NODE_ENV=production
MONGO_URI=your-production-mongodb-uri
JWT_SECRET=your-secure-jwt-secret
SESSION_SECRET=your-secure-session-secret
FRONTEND_URL=https://sociality-black.vercel.app
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-key
CLOUDINARY_API_SECRET=your-cloudinary-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## Google OAuth Configuration

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to "APIs & Services" > "Credentials"
3. Edit your OAuth 2.0 Client ID
4. Add these authorized redirect URIs:
   - `https://sociality-black.vercel.app/api/auth/google/callback`
   - `https://sociality-black.vercel.app/api/auth/google/popup/callback`

## MongoDB Configuration

Ensure your MongoDB connection string:
1. Uses MongoDB Atlas or a production-ready database
2. Has proper network access configured
3. Includes connection options for serverless environments

Example connection string:
```
mongodb+srv://username:<EMAIL>/sociality?retryWrites=true&w=majority&serverSelectionTimeoutMS=5000
```

## Deployment Steps

1. **Push your changes to GitHub**
2. **Redeploy on Vercel** (automatic if connected to GitHub)
3. **Check environment variables** are properly set
4. **Test the deployment**

## Testing the Fixes

After deployment, test these scenarios:

1. **Authentication**: Try logging in with Google OAuth
2. **API Endpoints**: Check if `/api/posts/feed` returns data
3. **Socket.IO**: Verify real-time messaging works
4. **Error Handling**: Check browser console for cleaner error messages

## Troubleshooting

### If Socket.IO still fails:
- Check browser console for connection errors
- Verify CORS settings in Vercel dashboard
- Ensure WebSocket support is enabled

### If API returns 500 errors:
- Check Vercel function logs
- Verify MongoDB connection
- Ensure all environment variables are set

### If authentication fails:
- Verify Google OAuth redirect URIs
- Check JWT_SECRET and SESSION_SECRET are set
- Ensure cookies are being set properly

## Key Changes Made

1. **CORS Origins**: Updated to include your actual domain
2. **Socket.IO Transports**: Polling prioritized for Vercel
3. **Error Handling**: Better error messages and logging
4. **Session Config**: Optimized for serverless environment
5. **Timeouts**: Increased for cold start compatibility

## Performance Optimizations

- Increased Socket.IO timeouts for serverless cold starts
- Optimized MongoDB connection settings
- Better error handling to prevent crashes
- Improved reconnection logic for Socket.IO

## Security Enhancements

- Proper CORS configuration
- Secure cookie settings for production
- Better token validation
- Environment variable validation

After following this guide, your Socket.IO connections should be stable and API errors should be resolved.
