# Production Environment Variables for Vercel Deployment
# Copy this file and set these environment variables in your Vercel dashboard

# Basic Configuration
NODE_ENV=production
PORT=5000

# Database Configuration
MONGO_URI=your-production-mongodb-connection-string

# JWT Configuration
JWT_SECRET=your-production-jwt-secret-key

# Session Configuration
SESSION_SECRET=your-production-session-secret-key

# Frontend URL (Your Vercel domain)
FRONTEND_URL=https://sociality-black.vercel.app

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Telegram Bot Configuration (Optional)
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_BOT_USERNAME=@your-bot-username

# Discord Bot Configuration (Optional)
DISCORD_BOT_TOKEN=your-discord-bot-token
DISCORD_CLIENT_ID=your-discord-client-id

# Federation Configuration (Optional)
FEDERATION_REGISTRY_URL=https://your-federation-registry.vercel.app
PLATFORM_URL=https://sociality-black.vercel.app
PLATFORM_NAME=sociality
FEDERATION_ENABLED=false
ENABLE_CROSS_PLATFORM=false

# Logging Configuration
LOG_LEVEL=error
ENABLE_SOCKET_LOGS=false

# Instructions:
# 1. Set these environment variables in your Vercel dashboard
# 2. Make sure MONGO_URI points to your production MongoDB instance
# 3. Generate secure random strings for JWT_SECRET and SESSION_SECRET
# 4. Update FRONTEND_URL to match your actual Vercel domain
# 5. Configure Google OAuth with your production domain in Google Console
